'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Storage, DataKeys } from '@/utils/storage';
import { ManufacturingApi } from '@/api/manufacturing';
import { PairingApi } from '@/api/pairing';
import { Task, Status } from '@/types/api';
import { t } from '@/locales';
import TaskCard from './TaskCard';

const MainScreen = () => {
  const [employeeName, setEmployeeName] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showMenu, setShowMenu] = useState(false);

  const router = useRouter();
  const manufacturingApi = new ManufacturingApi();
  const pairingApi = new PairingApi();

  useEffect(() => {
    checkEmployee();
    loadTasks();
  }, [selectedDate]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showMenu) {
        const target = event.target as Element;
        if (!target.closest('.menu-container')) {
          setShowMenu(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMenu]);

  const checkEmployee = async () => {
    try {
      const name = await Storage.read(DataKeys.EMPLOYEE_NAME);
      setEmployeeName(name);
    } catch {
      router.push('/pairing/qr-scan');
    }
  };

  const loadTasks = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Use real API to get tasks for the selected date
      const taskList = await manufacturingApi.getTasks(selectedDate);
      setTasks(taskList);
    } catch (err) {
      setError(t.couldNotLoadData);
      console.error('Error loading tasks:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const previousDay = () => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() - 1);
    setSelectedDate(newDate);
  };

  const nextDay = () => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + 1);
    setSelectedDate(newDate);
  };

  const handleToggleMenu = () => {
    setShowMenu(!showMenu);
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return t.today;
    } else if (date.toDateString() === yesterday.toDateString()) {
      return t.yesterday;
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return t.tomorrow;
    } else {
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric'
      });
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleLogout = async () => {
    if (confirm(t.confirmLogout)) {
      try {
        await pairingApi.logout();
        await Storage.deleteAll();
        router.push('/pairing/qr-scan');
      } catch (err) {
        console.error('Logout error:', err);
        // Even if logout fails, clear local storage and redirect
        await Storage.deleteAll();
        router.push('/pairing/qr-scan');
      }
    }
  };

  const activeTasks = tasks.filter(task => task.status !== Status.DONE);
  const completedTasks = tasks.filter(task => task.status === Status.DONE);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b relative">
        <div className="max-w-4xl mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
              <span className="text-white font-medium text-sm">
                {getInitials(employeeName)}
              </span>
            </div>
            <span className="font-medium text-gray-900">{employeeName}</span>
          </div>

          <div className="menu-container relative">
            <button
              onClick={handleToggleMenu}
              className="p-2 rounded-md hover:bg-gray-100 transition-colors"
              aria-label="Open menu"
            >
              <svg className="w-6 h-6" fill="none" stroke="black" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            {/* Menu Dropdown */}
            {showMenu && (
              <div className="absolute right-0 top-full mt-2 bg-white rounded-md shadow-lg border z-20 min-w-[150px]">
                <button
                  onClick={handleLogout}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-800 hover:bg-gray-100 transition-colors"
                >
                  {t.logout}
                </button>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto p-4">
        {/* Date Navigation */}
        <div className="flex items-center justify-between mb-6 bg-white text-black rounded-lg p-4 shadow-sm">
          <button
            onClick={previousDay}
            className="p-2 rounded-md hover:bg-gray-100 transition-colors active:bg-gray-200"
            aria-label="Previous day"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <h2 className="text-lg font-semibold text-center flex-1 mx-4">
            {formatDate(selectedDate)}
          </h2>

          <button
            onClick={nextDay}
            className="p-2 rounded-md hover:bg-gray-100 transition-colors active:bg-gray-200"
            aria-label="Next day"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-700">{error}</p>
            <button
              onClick={loadTasks}
              className="mt-2 text-red-600 hover:text-red-800 font-medium"
            >
              {t.retry}
            </button>
          </div>
        )}

        {/* Active Tasks */}
        <div className="space-y-4 mb-8">
          {activeTasks.length === 0 && !error && (
            <div className="text-center py-8 text-gray-800 font-medium">
              {t.noTasksForDate}
            </div>
          )}
          {activeTasks.map(task => (
            <TaskCard
              key={task.id}
              task={task}
              onTaskUpdate={loadTasks}
            />
          ))}
        </div>

        {/* Completed Tasks */}
        {completedTasks.length > 0 && (
          <>
            <div className="border-t pt-6">
              <h3 className="text-sm text-gray-800 mb-4 font-semibold">{t.completedTasks}</h3>
              <div className="space-y-4">
                {completedTasks.map(task => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    onTaskUpdate={loadTasks}
                  />
                ))}
              </div>
            </div>
          </>
        )}
      </main>
    </div>
  );
};

export default MainScreen;
