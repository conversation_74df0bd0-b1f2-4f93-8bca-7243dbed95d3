// Manufacturing API - Task management
import { AuthenticatedClient } from './client';
import { Task, Status, StatusReason } from '@/types/api';

export class ManufacturingApi {
  private client: AuthenticatedClient;

  constructor() {
    this.client = new AuthenticatedClient();
  }

  async getTasks(selectedDate: Date): Promise<Task[]> {
    const day = selectedDate.toISOString().split('T')[0]; // Format: YYYY-MM-DD
    const response = await this.client.get(`/manufacturing/tasks/list?day=${day}`);
    const data = await response.json();
    
    return data.map((item: any) => ({
      id: item.id,
      product: {
        id: item.product.id,
        name: item.product.name
      },
      status: item.status as Status,
      statusReason: item.statusReason as StatusReason | undefined,
      name: item.name,
      number: item.number,
      quantity: item.quantity,
      measurementUnit: {
        id: item.measurementUnit.id,
        name: item.measurementUnit.name
      },
      notes: item.notes,
      assignedWorkstations: item.assignedWorkstations || [],
      assignedEmployees: item.assignedEmployees || [],
      materials: item.materials || []
    }));
  }

  async setTaskInProgress(taskId: string): Promise<void> {
    await this.changeStatus(taskId, 'in-progress');
  }

  async setTaskStopped(taskId: string, reason: StatusReason): Promise<void> {
    await this.changeStatus(taskId, 'stopped', reason.toLowerCase());
  }

  async setTaskDone(taskId: string): Promise<void> {
    await this.changeStatus(taskId, 'done');
  }

  private async changeStatus(taskId: string, pathFragment: string, reason?: string): Promise<void> {
    const endpoint = `/manufacturing/tasks/${taskId}/${pathFragment}${reason ? `?reason=${reason}` : ''}`;
    const response = await this.client.post(endpoint);

    if (!response.ok) {
      throw new Error('Cannot update task status');
    }
  }


}
