'use client';

import { useState } from 'react';
import { Task, Status, StatusReason } from '@/types/api';
import { ManufacturingApi } from '@/api/manufacturing';
import { t } from '@/locales';

interface TaskCardProps {
  task: Task;
  onTaskUpdate: () => void;
}

const TaskCard = ({ task, onTaskUpdate }: TaskCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [showStopOptions, setShowStopOptions] = useState(false);
  
  const manufacturingApi = new ManufacturingApi();

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleTaskInProgress = async () => {
    setIsUpdating(true);
    try {
      await manufacturingApi.setTaskInProgress(task.id);
      onTaskUpdate();
    } catch (error) {
      alert(t.cantUpdateTask);
      console.error('Error updating task:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleTaskDone = async () => {
    setIsUpdating(true);
    try {
      await manufacturingApi.setTaskDone(task.id);
      onTaskUpdate();
    } catch (error) {
      alert(t.cantUpdateTask);
      console.error('Error updating task:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleTaskStopped = async (reason: StatusReason) => {
    setIsUpdating(true);
    setShowStopOptions(false);
    try {
      await manufacturingApi.setTaskStopped(task.id, reason);
      onTaskUpdate();
    } catch (error) {
      alert(t.cantUpdateTask);
      console.error('Error updating task:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusReasonText = (reason: StatusReason) => {
    switch (reason) {
      case StatusReason.PAUSED:
        return t.paused;
      case StatusReason.MISSING_MATERIAL:
        return t.missingMaterial;
      case StatusReason.BROKEN_EQUIPMENT:
        return t.brokenEquipment;
      case StatusReason.EQUIPMENT_UNAVAILABLE:
        return t.equipmentUnavailable;
      default:
        return '';
    }
  };

  const getMeasurementUnitText = (unit: string) => {
    switch (unit) {
      case 'pcs':
        return t.measurementUnitPiece;
      default:
        return unit;
    }
  };

  const renderActionButtons = () => {
    if (task.status === Status.TODO || task.status === Status.STOPPED) {
      return (
        <button
          onClick={handleTaskInProgress}
          disabled={isUpdating}
          className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 font-medium"
        >
          {isUpdating ? t.loading : (task.status === Status.TODO ? t.start : t.resume)}
        </button>
      );
    }

    if (task.status === Status.IN_PROGRESS) {
      return (
        <div className="flex gap-2 w-full">
          <button
            onClick={() => setShowStopOptions(true)}
            disabled={isUpdating}
            className="flex-1 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 disabled:bg-gray-400 font-medium"
          >
            {t.stop}
          </button>
          <button
            onClick={handleTaskDone}
            disabled={isUpdating}
            className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 font-medium"
          >
            {isUpdating ? t.loading : t.markCompleted}
          </button>
        </div>
      );
    }

    return null;
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border p-4">
        {/* Header */}
        <div className="flex justify-between items-start mb-2">
          <span className="text-sm text-gray-800 font-medium">{task.number}</span>
          <span className="text-sm text-gray-800">
            {task.assignedWorkstations.length > 0 ? task.assignedWorkstations[0].name : ''}
          </span>
        </div>

        {/* Product and Expand Button */}
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-semibold text-gray-900">{task.product.name}</h3>
          <button
            onClick={toggleExpanded}
            className="p-1 rounded-md hover:bg-gray-100"
          >
            <svg
              className={`w-5 h-5 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>

        {/* Task Details */}
        <p className="text-gray-800 mb-4">
          {task.name} • {task.quantity} {getMeasurementUnitText(task.measurementUnit.name)}
        </p>

        {/* Materials (Expanded) */}
        {isExpanded && task.materials.length > 0 && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-semibold text-blue-900">{t.materials}</span>
              <span className="text-sm font-medium text-blue-800">{t.neededQuantity}</span>
            </div>
            <div className="space-y-1">
              {task.materials.map((material, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm text-blue-800">{material.name}</span>
                  <span className="text-sm text-blue-800 font-medium">{material.quantity}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Notes */}
        {task.notes && (
          <div className="mb-4">
            <span className="text-sm text-gray-800 block mb-1 font-semibold">{t.notes}</span>
            <p className="text-sm text-gray-800">{task.notes}</p>
          </div>
        )}

        {/* Status Reason (if stopped) */}
        {task.status === Status.STOPPED && task.statusReason && (
          <div className="mb-4 p-3 bg-orange-100 border border-orange-300 rounded-md">
            <span className="text-sm text-orange-900 font-medium">
              {getStatusReasonText(task.statusReason)}
            </span>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          {renderActionButtons()}
        </div>
      </div>

      {/* Stop Options Modal */}
      {showStopOptions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end justify-center z-50">
          <div className="bg-white rounded-t-lg w-full max-w-md p-4 space-y-2">
            <h3 className="text-lg font-semibold mb-4 text-center text-gray-900">Select Stop Reason</h3>

            <button
              onClick={() => handleTaskStopped(StatusReason.PAUSED)}
              className="w-full flex items-center p-3 text-left hover:bg-gray-100 rounded-md text-gray-900"
            >
              <svg className="w-5 h-5 mr-3 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6" />
              </svg>
              {t.paused}
            </button>

            <button
              onClick={() => handleTaskStopped(StatusReason.MISSING_MATERIAL)}
              className="w-full flex items-center p-3 text-left hover:bg-gray-100 rounded-md text-gray-900"
            >
              <svg className="w-5 h-5 mr-3 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              {t.missingMaterial}
            </button>

            <button
              onClick={() => handleTaskStopped(StatusReason.BROKEN_EQUIPMENT)}
              className="w-full flex items-center p-3 text-left hover:bg-gray-100 rounded-md text-gray-900"
            >
              <svg className="w-5 h-5 mr-3 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {t.brokenEquipment}
            </button>

            <button
              onClick={() => handleTaskStopped(StatusReason.EQUIPMENT_UNAVAILABLE)}
              className="w-full flex items-center p-3 text-left hover:bg-gray-100 rounded-md text-gray-900"
            >
              <svg className="w-5 h-5 mr-3 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
              </svg>
              {t.equipmentUnavailable}
            </button>

            <button
              onClick={() => setShowStopOptions(false)}
              className="w-full p-3 text-center text-gray-900 hover:bg-gray-100 rounded-md mt-4 font-medium"
            >
              {t.cancel}
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default TaskCard;
