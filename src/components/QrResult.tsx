'use client';

import { use<PERSON>tom } from 'jotai';
import { scannedQrCodeAtom } from '@/atoms/qrCodeAtoms';

const QrResult = () => {
  const [scannedQrCode] = useAtom(scannedQrCodeAtom);

  if (!scannedQrCode) {
    return null;
  }

  // Try to determine if the scanned code is a URL
  const isUrl = scannedQrCode.startsWith('http://') || scannedQrCode.startsWith('https://');

  return (
    <div className="mt-8 w-full max-w-md mx-auto">
      <div className="bg-white shadow-md rounded-lg p-6">
        <h2 className="text-xl font-bold mb-4 text-gray-900">Scan Result</h2>
        
        <div className="mb-4">
          <p className="text-gray-700 break-all">{scannedQrCode}</p>
        </div>
        
        {isUrl && (
          <a
            href={scannedQrCode}
            target="_blank"
            rel="noopener noreferrer"
            className="block w-full text-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded"
          >
            Open URL
          </a>
        )}
      </div>
    </div>
  );
};

export default QrResult;
