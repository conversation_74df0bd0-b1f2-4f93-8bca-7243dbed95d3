// Manufacturing API - Task management
import { AuthenticatedClient } from './client';
import { Task, Status, StatusReason } from '@/types/api';

export class ManufacturingApi {
  private client: AuthenticatedClient;

  constructor() {
    this.client = new AuthenticatedClient();
  }

  async getTasks(selectedDate: Date): Promise<Task[]> {
    const day = selectedDate.toISOString().split('T')[0]; // Format: YYYY-MM-DD
    const response = await this.client.get(`/manufacturing/tasks/list?day=${day}`);
    const data = await response.json();
    
    return data.map((item: any) => ({
      id: item.id,
      product: {
        id: item.product.id,
        name: item.product.name
      },
      status: item.status as Status,
      statusReason: item.statusReason as StatusReason | undefined,
      name: item.name,
      number: item.number,
      quantity: item.quantity,
      measurementUnit: {
        id: item.measurementUnit.id,
        name: item.measurementUnit.name
      },
      notes: item.notes,
      assignedWorkstations: item.assignedWorkstations || [],
      assignedEmployees: item.assignedEmployees || [],
      materials: item.materials || []
    }));
  }

  async setTaskInProgress(taskId: string): Promise<void> {
    await this.changeStatus(taskId, 'in-progress');
  }

  async setTaskStopped(taskId: string, reason: StatusReason): Promise<void> {
    await this.changeStatus(taskId, 'stopped', reason.toLowerCase());
  }

  async setTaskDone(taskId: string): Promise<void> {
    await this.changeStatus(taskId, 'done');
  }

  private async changeStatus(taskId: string, pathFragment: string, reason?: string): Promise<void> {
    const endpoint = `/manufacturing/tasks/${taskId}/${pathFragment}${reason ? `?reason=${reason}` : ''}`;
    const response = await this.client.post(endpoint);
    
    if (!response.ok) {
      throw new Error('Cannot update task status');
    }
  }

  // Mock data for development/testing
  async getMockTasks(selectedDate?: Date): Promise<Task[]> {
    // Create a comprehensive set of mock tasks for different dates
    const allMockTasks: Task[] = [
      // Today's tasks
      {
        id: 'today-1',
        product: { id: '1', name: 'Chair - Oak / Medium' },
        status: Status.STOPPED,
        statusReason: StatusReason.MISSING_MATERIAL,
        name: 'Paint with lacquer',
        number: 'MO-123 / 1 / 1',
        quantity: 10,
        measurementUnit: { id: 'PIECE', name: 'pcs' },
        notes: 'Needs to be ready by May 10th',
        assignedWorkstations: [{ id: '1', name: 'Painting 121' }],
        assignedEmployees: [{ id: '1', name: 'Jerome Bell' }],
        materials: [
          { id: '1', name: 'Screw 10', quantity: 10 },
          { id: '2', name: 'Wood frame', quantity: 1 },
          { id: '3', name: 'Wood legs', quantity: 10 }
        ]
      },
      {
        id: 'today-2',
        product: { id: '2', name: 'Table - Walnut / Large' },
        status: Status.TODO,
        name: 'Cut to dimensions',
        number: 'MO-321 / 2 / 2',
        quantity: 10,
        measurementUnit: { id: 'PIECE', name: 'pcs' },
        notes: 'Needs to be ready by May 10th',
        assignedWorkstations: [{ id: '2', name: 'Cutting 232' }],
        assignedEmployees: [{ id: '1', name: 'Jerome Bell' }],
        materials: [
          { id: '1', name: 'Screw 10', quantity: 10 },
          { id: '2', name: 'Wood frame', quantity: 1 },
          { id: '3', name: 'Wood legs', quantity: 10 }
        ]
      },
      {
        id: 'today-3',
        product: { id: '3', name: 'Desk - Cherry / Small' },
        status: Status.DONE,
        name: 'Assembly',
        number: 'MO-567 / 1 / 1',
        quantity: 5,
        measurementUnit: { id: 'PIECE', name: 'pcs' },
        notes: 'Completed this morning',
        assignedWorkstations: [{ id: '3', name: 'Assembly 101' }],
        assignedEmployees: [{ id: '1', name: 'Jerome Bell' }],
        materials: [
          { id: '4', name: 'Wood panel', quantity: 2 },
          { id: '5', name: 'Metal brackets', quantity: 4 }
        ]
      }
    ];

    // If no date is provided, return today's tasks
    if (!selectedDate) {
      return allMockTasks;
    }

    // Generate different tasks based on the selected date
    const today = new Date();
    const dayDiff = Math.floor((selectedDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    if (dayDiff === 0) {
      // Today - return all tasks
      return allMockTasks;
    } else if (dayDiff === -1) {
      // Yesterday - return completed tasks
      return [
        {
          id: 'yesterday-1',
          product: { id: '4', name: 'Bookshelf - Pine / Large' },
          status: Status.DONE,
          name: 'Sanding',
          number: 'MO-789 / 1 / 2',
          quantity: 3,
          measurementUnit: { id: 'PIECE', name: 'pcs' },
          notes: 'Finished yesterday evening',
          assignedWorkstations: [{ id: '4', name: 'Sanding 201' }],
          assignedEmployees: [{ id: '1', name: 'Jerome Bell' }],
          materials: [
            { id: '6', name: 'Sandpaper 120', quantity: 5 },
            { id: '7', name: 'Wood stain', quantity: 1 }
          ]
        },
        {
          id: 'yesterday-2',
          product: { id: '5', name: 'Cabinet - Maple / Medium' },
          status: Status.DONE,
          name: 'Drilling holes',
          number: 'MO-456 / 3 / 1',
          quantity: 8,
          measurementUnit: { id: 'PIECE', name: 'pcs' },
          notes: 'All holes drilled perfectly',
          assignedWorkstations: [{ id: '5', name: 'Drilling 301' }],
          assignedEmployees: [{ id: '1', name: 'Jerome Bell' }],
          materials: [
            { id: '8', name: 'Drill bits', quantity: 3 },
            { id: '9', name: 'Wood panels', quantity: 8 }
          ]
        }
      ];
    } else if (dayDiff === 1) {
      // Tomorrow - return planned tasks
      return [
        {
          id: 'tomorrow-1',
          product: { id: '6', name: 'Wardrobe - Oak / Large' },
          status: Status.TODO,
          name: 'Initial cutting',
          number: 'MO-999 / 1 / 1',
          quantity: 2,
          measurementUnit: { id: 'PIECE', name: 'pcs' },
          notes: 'Start early morning',
          assignedWorkstations: [{ id: '2', name: 'Cutting 232' }],
          assignedEmployees: [{ id: '1', name: 'Jerome Bell' }],
          materials: [
            { id: '10', name: 'Oak planks', quantity: 12 },
            { id: '11', name: 'Cutting blades', quantity: 2 }
          ]
        },
        {
          id: 'tomorrow-2',
          product: { id: '7', name: 'Stool - Birch / Small' },
          status: Status.TODO,
          name: 'Turning legs',
          number: 'MO-888 / 2 / 1',
          quantity: 6,
          measurementUnit: { id: 'PIECE', name: 'pcs' },
          notes: 'Use lathe machine #3',
          assignedWorkstations: [{ id: '6', name: 'Turning 401' }],
          assignedEmployees: [{ id: '1', name: 'Jerome Bell' }],
          materials: [
            { id: '12', name: 'Birch rods', quantity: 24 },
            { id: '13', name: 'Turning tools', quantity: 1 }
          ]
        },
        {
          id: 'tomorrow-3',
          product: { id: '8', name: 'Mirror Frame - Walnut / Medium' },
          status: Status.TODO,
          name: 'Carving details',
          number: 'MO-777 / 1 / 3',
          quantity: 1,
          measurementUnit: { id: 'PIECE', name: 'pcs' },
          notes: 'Intricate pattern required',
          assignedWorkstations: [{ id: '7', name: 'Carving 501' }],
          assignedEmployees: [{ id: '1', name: 'Jerome Bell' }],
          materials: [
            { id: '14', name: 'Walnut block', quantity: 1 },
            { id: '15', name: 'Carving chisels', quantity: 8 }
          ]
        }
      ];
    } else {
      // Other days - return fewer or no tasks
      if (Math.abs(dayDiff) <= 3) {
        return [
          {
            id: `day-${dayDiff}-1`,
            product: { id: '9', name: 'Generic Item' },
            status: Status.TODO,
            name: 'Scheduled task',
            number: `MO-${Math.abs(dayDiff)}00 / 1 / 1`,
            quantity: 1,
            measurementUnit: { id: 'PIECE', name: 'pcs' },
            notes: `Task scheduled for ${selectedDate.toLocaleDateString()}`,
            assignedWorkstations: [{ id: '1', name: 'General Station' }],
            assignedEmployees: [{ id: '1', name: 'Jerome Bell' }],
            materials: []
          }
        ];
      } else {
        // No tasks for dates too far in the past or future
        return [];
      }
    }
  }
}
