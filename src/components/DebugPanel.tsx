'use client';

import { useState, useEffect } from 'react';
import { debugInstallPwa, isPwaInstalled, isPwaInstallable } from '@/utils/pwaUtils';
import { log } from '@/utils/logger';

const DebugPanel = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [showDebug, setShowDebug] = useState(false);

  useEffect(() => {
    // Only show debug panel if URL has ?debug=true
    const checkDebugMode = () => {
      if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        const isDebugMode = urlParams.get('debug') === 'true';
        setShowDebug(isDebugMode);

        // If debug mode is not active, make sure we're not showing the panel
        if (!isDebugMode && isOpen) {
          setIsOpen(false);
        }
      }
    };

    checkDebugMode();

    // Check again when URL changes (using a more efficient approach)
    const handleLocationChange = () => {
      setTimeout(checkDebugMode, 100); // Small delay to ensure URL is updated
    };

    window.addEventListener('popstate', handleLocationChange);
    window.addEventListener('pushstate', handleLocationChange);
    window.addEventListener('replacestate', handleLocationChange);

    return () => {
      window.removeEventListener('popstate', handleLocationChange);
      window.removeEventListener('pushstate', handleLocationChange);
      window.removeEventListener('replacestate', handleLocationChange);
    };
  }, [isOpen]);

  const togglePanel = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      fetchLogs();
    }
  };

  const fetchLogs = async () => {
    try {
      const response = await fetch('/api/log');
      const data = await response.json();
      setLogs(data.logs || []);
    } catch (error) {
      console.error('Error fetching logs:', error);
    }
  };

  const handleInstallClick = () => {
    log('debugPanel', { action: 'install_clicked' });
    debugInstallPwa();
  };

  const handleRefreshClick = () => {
    log('debugPanel', { action: 'refresh_clicked' });
    window.location.reload();
  };

  const handleClearLogsClick = async () => {
    log('debugPanel', { action: 'clear_logs_clicked' });
    try {
      await fetch('/api/log', { method: 'DELETE' });
      setLogs([]);
    } catch (error) {
      console.error('Error clearing logs:', error);
    }
  };

  const checkInstallStatus = () => {
    const installed = isPwaInstalled();
    const installable = isPwaInstallable();
    const installedFlag = localStorage.getItem('pwa-installed');
    log('debugPanel', { action: 'check_status', installed, installable, installedFlag });
    alert(`App installed: ${installed}\nApp installable: ${installable}\nInstalled flag: ${installedFlag}`);
  };

  const forceSetInstalled = () => {
    localStorage.setItem('pwa-installed', 'true');
    log('debugPanel', { action: 'force_set_installed' });
    alert('Set installed flag to true. Reloading page...');
    setTimeout(() => window.location.reload(), 1000);
  };

  const clearInstalled = () => {
    localStorage.removeItem('pwa-installed');
    log('debugPanel', { action: 'clear_installed' });
    alert('Cleared installed flag. Reloading page...');
    setTimeout(() => window.location.reload(), 1000);
  };

  const clearAllDebugData = () => {
    localStorage.removeItem('pwa-installed');
    localStorage.removeItem('fabrication_debug_bypass');
    localStorage.removeItem('installPromptShown');
    log('debugPanel', { action: 'clear_all_debug_data' });
    alert('Cleared all debug data. Reloading page...');
    setTimeout(() => window.location.reload(), 1000);
  };

  // Don't render anything if debug mode is not enabled
  if (!showDebug) {
    return null;
  }

  if (!isOpen) {
    return (
      <button
        onClick={togglePanel}
        className="fixed bottom-4 right-4 bg-gray-800 text-white p-2 rounded-full shadow-lg z-50"
        aria-label="Open debug panel"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end sm:items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-lg max-h-[80vh] flex flex-col">
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-lg font-semibold text-gray-900">PWA Debug Panel</h2>
          <button
            onClick={togglePanel}
            className="text-gray-600 hover:text-gray-800"
            aria-label="Close debug panel"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-4 border-b border-gray-200">
          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={handleInstallClick}
              className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
            >
              Force Install Prompt
            </button>
            <button
              onClick={checkInstallStatus}
              className="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700"
            >
              Check Install Status
            </button>
            <button
              onClick={forceSetInstalled}
              className="bg-indigo-600 text-white py-2 px-4 rounded hover:bg-indigo-700"
            >
              Force Set Installed
            </button>
            <button
              onClick={clearInstalled}
              className="bg-orange-600 text-white py-2 px-4 rounded hover:bg-orange-700"
            >
              Clear Installed Flag
            </button>
            <button
              onClick={handleRefreshClick}
              className="bg-yellow-600 text-white py-2 px-4 rounded hover:bg-yellow-700"
            >
              Refresh Page
            </button>
            <button
              onClick={fetchLogs}
              className="bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700"
            >
              Refresh Logs
            </button>
            <button
              onClick={clearAllDebugData}
              className="bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700"
            >
              Clear All Debug Data
            </button>
            <button
              onClick={handleClearLogsClick}
              className="bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700"
            >
              Clear Logs
            </button>
          </div>
        </div>

        <div className="flex-1 overflow-auto p-4">
          <h3 className="text-md font-semibold mb-2 text-gray-900">Logs:</h3>
          <div className="bg-gray-100 p-3 rounded text-xs font-mono h-[300px] overflow-y-auto">
            {logs.length === 0 ? (
              <p className="text-gray-700">No logs available</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1 pb-1 border-b border-gray-200 text-gray-900">
                  {log}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugPanel;
