'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { PairingApi } from '@/api/pairing';
import { PairingInit } from '@/types/api';
import { Storage, DataKeys } from '@/utils/storage';
import { t } from '@/locales';

interface ConfirmationScreenProps {
  initiationToken: string;
}

const ConfirmationScreen = ({ initiationToken }: ConfirmationScreenProps) => {
  const [pairingInit, setPairingInit] = useState<PairingInit | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isConfirming, setIsConfirming] = useState(false);
  
  const router = useRouter();
  const pairingApi = new PairingApi();

  useEffect(() => {
    if (!initiationToken) {
      setError(t.initiationTokenIsNull);
      setIsLoading(false);
      return;
    }

    loadPairingInit();
  }, [initiationToken]);

  const loadPairingInit = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await pairingApi.initiate(initiationToken);
      setPairingInit(result);
    } catch (err) {
      setError(`${t.couldNotLoadData}: ${err}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirm = async () => {
    if (!pairingInit) return;

    setIsConfirming(true);
    try {
      const accessToken = await pairingApi.confirm(pairingInit.confirmationToken);
      
      // Store credentials
      await Storage.write(DataKeys.API_TOKEN, accessToken.token);
      await Storage.write(DataKeys.EMPLOYEE_NAME, pairingInit.employeeName);
      
      // Navigate to main screen
      router.push('/main');
    } catch (err) {
      setError(`${t.error}: ${err}`);
      // On error, go back to QR scan
      setTimeout(() => {
        router.push('/pairing/qr-scan');
      }, 2000);
    } finally {
      setIsConfirming(false);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const openPrivacyPolicy = () => {
    window.open('https://fabriqon-app-prod.web.app/privacy_policy_en.html', '_blank');
  };

  const openTermsAndConditions = () => {
    window.open('https://fabriqon-app-prod.web.app/t_and_c_en.html', '_blank');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-800 font-medium">{t.loading}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <div className="mb-4">
            <svg className="w-12 h-12 text-red-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">{t.error}</h2>
          <p className="text-gray-800 mb-6">{error}</p>
          <button
            onClick={loadPairingInit}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 font-medium"
          >
            {t.retry}
          </button>
        </div>
      </div>
    );
  }

  if (!pairingInit) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">{t.error}</h2>
          <p className="text-gray-800">{t.initiationTokenIsNull}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-3">
          <h1 className="text-lg font-semibold text-gray-900">{t.accountConfirmation}</h1>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-md mx-auto p-4 pt-20">
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          {/* Employee Avatar */}
          <div className="mb-6">
            <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-white font-semibold text-lg">
                {getInitials(pairingInit.employeeName)}
              </span>
            </div>
            <h2 className="text-xl font-semibold text-gray-900">
              {pairingInit.employeeName}
            </h2>
          </div>

          {/* Terms and Conditions */}
          <div className="mb-8 text-sm text-gray-800 space-y-2">
            <p>{t.confirmationOfTAndC}</p>
            <div className="space-y-1">
              <button
                onClick={openPrivacyPolicy}
                className="text-blue-600 hover:text-blue-800 underline block"
              >
                {t.privacyPolicy}
              </button>
              <button
                onClick={openTermsAndConditions}
                className="text-blue-600 hover:text-blue-800 underline block"
              >
                {t.tAndC}
              </button>
            </div>
          </div>

          {/* Confirm Button */}
          <button
            onClick={handleConfirm}
            disabled={isConfirming}
            className="w-full bg-yellow-500 text-white py-3 px-4 rounded-md hover:bg-yellow-600 disabled:bg-gray-400 font-medium text-lg"
          >
            {isConfirming ? t.loading : t.confirmAccount}
          </button>
        </div>
      </main>
    </div>
  );
};

export default ConfirmationScreen;
